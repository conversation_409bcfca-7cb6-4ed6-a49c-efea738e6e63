# **App Name**: Rawnak Sales

## Core Features:

- Dashboard: Dashboard overview displaying key metrics such as total sales, outstanding debts, and inventory status.
- Product Management: Product catalog management: Add, edit, and categorize products.
- Sales Recording: Sales recording interface: Record sales transactions, apply discounts, and manage customer debts.
- Inventory Tracking: Inventory tracking: Monitor stock levels, set low stock alerts, and record stock adjustments.
- Debt Management: Debt management: Track customer debts, record payments, and generate debt reports.
- User Management: Role-based user accounts: Manage employee access with different permission levels.

## Style Guidelines:

- Primary color: A vibrant blue (#29ABE2) to convey trust and modernity.
- Background color: A very light gray (#F0F4F7) to provide a clean and uncluttered interface.
- Accent color: A warm orange (#F2994A) to highlight key actions and calls to action.
- Body and headline font: 'Inter', a grotesque-style sans-serif for a modern and objective look.
- Use a set of simple, consistent icons, with filled styles to improve scannability and visual appeal.
- Maintain a consistent layout across all pages, with clear sections and intuitive navigation.  Use white space generously to enhance readability.
- Incorporate subtle transitions and animations to provide feedback and guide the user through the interface. Animations should be functional, not distracting.