{"AppSidebar": {"salesManagement": "Sales Management", "dashboard": "Dashboard", "products": "Products", "sales": "Sales", "inventory": "Inventory", "debts": "Debts", "users": "Users", "settings": "Settings", "storeOwner": "Store Owner", "logout": "Log Out"}, "SettingsPage": {"title": "Settings", "description": "Configure your application and preferences.", "storeInfoTitle": "Store Information", "storeInfoDescription": "Update your store's basic details.", "storeNameLabel": "Store Name", "storeAddressLabel": "Address", "storeContactLabel": "Contact Phone", "saveStoreInfoButton": "Save Store Info", "accountSecurityTitle": "Account Security", "accountSecurityDescription": "Manage your account security settings.", "changePasswordButton": "Change Password", "twoFactorAuthLabel": "Two-Factor Authentication", "twoFactorAuthDescription": "Enhance your account security.", "appearanceTitle": "Appearance", "appearanceDescription": "Customize the look and feel of the application.", "darkModeLabel": "Dark Mode", "darkModeDescription": "Toggle between light and dark themes.", "languageLabel": "Language", "selectLanguagePlaceholder": "Select language", "english": "English", "arabic": "Arabic", "currencySettingsTitle": "<PERSON><PERSON><PERSON><PERSON>", "currencySettingsDescription": "Choose your preferred display currency.", "displayCurrencyLabel": "<PERSON><PERSON>lay Currency", "selectCurrencyPlaceholder": "Select currency", "notificationsTitle": "Notifications", "notificationsDescription": "Manage your notification preferences.", "lowStockAlertsLabel": "Low Stock Alerts", "lowStockAlertsDescription": "Receive notifications for products running low on stock.", "debtRemindersLabel": "Debt Reminders", "debtRemindersDescription": "Get reminders for overdue customer debts.", "emailForUpdatesLabel": "Email for Important Updates", "saveNotificationSettingsButton": "Save Notification Settings", "dataExportTitle": "Data & Export", "dataExportDescription": "Manage your application data.", "exportSalesButton": "Export Sales Data (CSV)", "exportProductsButton": "Export Product Data (CSV)", "backupDatabaseButton": "Backup Entire Database"}, "DashboardPage": {"title": "Dashboard", "description": "Overview of your sales and store performance.", "addProduct": "Add Product", "newSale": "New Sale", "totalSales": "Total Sales", "outstandingDebts": "Outstanding Debts", "activeProducts": "Active Products", "customers": "Customers", "salesTrendTitle": "Sales Trend", "salesTrendDescription": "Monthly sales and expenses overview.", "salesLabel": "Sales", "expensesLabel": "Expenses", "recentActivityTitle": "Recent Activity", "recentActivityDescription": "Latest sales and inventory updates.", "lowStockAlertsTitle": "Low Stock Alerts", "lowStockAlertsDescription": "Products that need restocking soon.", "tableHeaderProduct": "Product", "tableHeaderCategory": "Category", "tableHeaderStock": "Stock", "tableHeaderAction": "Action", "restockButton": "Restock", "salePrefix": "Sale", "productLowStockSuffix": "Low Stock", "itemsRemainingSuffix": "items remaining", "newProductAdded": "New Product Added", "debtPaymentReceived": "Debt Payment Received", "minutesAgo": "{count, plural, one {# min ago} other {# mins ago}}", "hoursAgo": "{count, plural, one {# hr ago} other {# hrs ago}}", "yesterday": "Yesterday", "fromLastMonth": "from last month", "newDebtsThisWeek": "new debts this week", "addedThisMonth": "added this month", "newCustomers": "new customers"}, "ProductsPage": {"title": "Product Management", "description": "Manage your product catalog, categories, and pricing.", "addNewProduct": "Add New Product", "editProductTitle": "Edit Product", "addProductTitle": "Add New Product", "editProductDescription": "Update the details of the existing product.", "addProductDescription": "Fill in the details to add a new product to your catalog.", "nameLabel": "Name", "categoryLabel": "Category", "selectCategoryPlaceholder": "Select a category", "grainsCategory": "Grains", "fruitsCategory": "Fruits", "dairyCategory": "Dairy", "bakeryCategory": "<PERSON><PERSON>", "meatCategory": "Meat", "beveragesCategory": "Beverages", "descriptionLabel": "Description", "optionalProductDescription": "Optional product description", "purchasePriceLabelUSD": "Purchase Price (USD)", "salePriceLabelUSD": "Sale Price (USD)", "initialStockLabel": "Initial Stock", "saveChangesButton": "Save Changes", "addProductButton": "Add Product", "tableHeaderName": "Name", "tableHeaderCategory": "Category", "tableHeaderPrice": "Price ({currencySymbol})", "tableHeaderStock": "Stock", "tableHeaderStatus": "Status", "tableHeaderActions": "Actions", "statusInStock": "In Stock", "statusLowStock": "Low Stock", "statusOutOfStock": "Out of Stock", "editAction": "Edit", "deleteAction": "Delete", "noProductsFound": "No products found. Add your first product!"}, "SalesPage": {"title": "Sales Recording", "description": "Record new sales transactions and manage customer purchases.", "newSaleCardTitle": "New Sale", "customerLabel": "Customer", "selectCustomerPlaceholder": "Select or add customer", "addNewCustomerOption": "Add New Customer...", "saleDateLabel": "Sale Date", "addProductLabel": "Add Product", "searchProductPlaceholder": "Search or select product", "quantityLabel": "Qty", "tableHeaderProduct": "Product", "tableHeaderQuantity": "Qty", "tableHeaderPrice": "Price ({currencySymbol})", "tableHeaderTotal": "Total ({currencySymbol})", "tableHeaderAction": "Action", "noItemsInCart": "No items in cart", "discountLabel": "Discount (%)", "paymentStatusLabel": "Payment Status", "selectStatusPlaceholder": "Select status", "paidStatus": "Paid", "debtStatus": "Debt", "subtotalLabel": "Subtotal: {amount}", "discountAmountLabel": "Discount: {amount}", "totalAmountLabel": "Total: {amount}", "recordSaleButton": "Record Sale", "recentSalesCardTitle": "Recent Sales", "tableHeaderID": "ID", "tableHeaderCustomer": "Customer", "tableHeaderTotalAmount": "Total ({currencySymbol})", "tableHeaderStatus": "Status", "tableHeaderActions": "Actions", "actionViewDetails": "View Details", "actionPrintReceipt": "Print Receipt", "noSalesRecorded": "No sales recorded yet.", "viewAllSalesButton": "View All Sales"}, "InventoryPage": {"title": "Inventory Tracking", "description": "Monitor stock levels, set alerts, and record adjustments.", "searchProductPlaceholder": "Search products...", "filterButton": "Filter", "adjustStockModalTitle": "Adjust Stock: {productName}", "adjustStockModalDescription": "Current stock: {stockCount}. Record new arrivals or stock changes.", "adjustmentTypeLabel": "Adjustment Type", "selectTypePlaceholder": "Select type", "addStockOption": "Add Stock (New Arrival)", "removeStockOption": "Remove Stock (Damage/Expiry)", "setQuantityOption": "Set to Specific Quantity (Count)", "quantityLabel": "Quantity", "notesLabel": "Notes (Optional)", "notesPlaceholder": "e.g., Supplier invoice #123", "adjustStockButton": "Adjust Stock", "tableHeaderProductName": "Product Name", "tableHeaderCategory": "Category", "tableHeaderCurrentStock": "Current Stock", "tableHeaderLowStockThreshold": "Low Stock Threshold", "tableHeaderStatus": "Status", "tableHeaderActions": "Actions", "statusOutOfStock": "Out of Stock", "statusLowStock": "Low Stock", "statusInStock": "In Stock", "actionAdjust": "Adjust", "noInventoryItems": "No inventory items found. Add products to see them here."}, "DebtsPage": {"title": "Debt Management", "description": "Track customer debts, record payments, and generate reports.", "searchCustomersPlaceholder": "Search customers...", "filterButton": "Filter", "generateReportButton": "Generate Report", "recordPaymentModalTitle": "Record Payment for {customerName}", "recordPaymentModalDescription": "Current debt: {debtAmount}. Record a new payment.", "paymentAmountLabel": "Payment Amount ({currencySymbol})", "paymentDateLabel": "Payment Date", "paymentNotesLabel": "Notes (Optional)", "paymentNotesPlaceholder": "e.g., Cash payment", "recordPaymentButton": "Record Payment", "tableHeaderCustomerName": "Customer Name", "tableHeaderTotalDebt": "Total Debt ({currencySymbol})", "tableHeaderLastPayment": "Last Payment Date", "tableHeaderStatus": "Status", "tableHeaderActions": "Actions", "statusPaid": "Paid", "statusPending": "Pending", "statusOverdue": "Overdue", "actionRecordPayment": "Record Payment", "actionViewHistory": "View History", "noOutstandingDebts": "No outstanding debts. Great job!"}, "UsersPage": {"title": "User Management", "description": "Manage employee accounts and their permission levels.", "addNewUser": "Add New User", "editUserModalTitle": "Edit User", "addUserModalTitle": "Add New User", "editUserModalDescription": "Update user details and permissions.", "addUserModalDescription": "Fill in the details to create a new user account.", "fullNameLabel": "Full Name", "emailLabel": "Email", "passwordLabel": "Password", "roleLabel": "Role", "selectRolePlaceholder": "Select a role", "adminRole": "Admin", "employeeRole": "Employee", "statusLabel": "Status", "selectStatusPlaceholder": "Select status", "activeStatus": "Active", "inactiveStatus": "Inactive", "permissionsLabel": "Permissions", "manageSalesPermission": "Manage Sales", "manageProductsPermission": "Manage Products", "manageInventoryPermission": "Manage Inventory", "manageUsersPermission": "Manage Users (Admin only)", "saveChangesButton": "Save Changes", "createUserButton": "Create User", "tableHeaderName": "Name", "tableHeaderEmail": "Email", "tableHeaderRole": "Role", "tableHeaderStatus": "Status", "tableHeaderActions": "Actions", "actionEditUser": "Edit User", "actionResetPassword": "Reset Password", "actionDeleteUser": "Delete User", "noUsersFound": "No users found. Add your first user!"}, "General": {"cancel": "Cancel"}}