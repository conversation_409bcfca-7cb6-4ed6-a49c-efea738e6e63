
import {getRequestConfig} from 'next-intl/server';
import {notFound} from 'next/navigation';

// Supported locales
const locales = ['en', 'ar'];

export default getRequestConfig(async ({locale}) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) {
    notFound();
  }

  let messages;
  try {
    // The `default` is important when importing JSON with ES modules
    messages = (await import(`./messages/${locale}.json`)).default;
  } catch (error) {
    notFound();
  }

  if (!messages || typeof messages !== 'object' || Object.keys(messages).length === 0) {
    notFound();
  }

  return {
    messages
  };
});
