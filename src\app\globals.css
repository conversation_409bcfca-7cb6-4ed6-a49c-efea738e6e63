
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  /* font-family: Arial, Helvetica, sans-serif; */ /* Replaced by Inter from next/font */
}

@layer base {
  :root {
    --background: 206 27% 96%; /* #F0F4F7 */
    --foreground: 215 25% 27%; /* #3A414C */

    --card: 0 0% 100%; /* white */
    --card-foreground: 215 25% 27%; /* #3A414C */

    --popover: 0 0% 100%; /* white */
    --popover-foreground: 215 25% 27%; /* #3A414C */

    --primary: 199 78% 52%; /* #29ABE2 */
    --primary-foreground: 210 40% 98%; /* #F8FAFC */

    --secondary: 210 20% 92%; /* #EAEFF3 - Lighter gray for secondary elements */
    --secondary-foreground: 215 25% 27%; /* #3A414C */

    --muted: 210 20% 92%; /* #EAEFF3 */
    --muted-foreground: 215 15% 55%; /* #7E8A9C - Lighter text for muted content */

    --accent: 26 87% 62%; /* #F2994A */
    --accent-foreground: 26 70% 15%; /* #4A2A0F - Darker for good contrast on accent */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 210 20% 85%; /* #D1D9E0 */
    --input: 0 0% 100%; /* white for inputs */
    --ring: 199 78% 52%; /* Primary color for focus rings */

    --chart-1: 199 78% 52%;
    --chart-2: 26 87% 62%;
    --chart-3: 120 60% 50%; /* A green for variety */
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar specific theme variables, aligning with the light theme */
    --sidebar-background: 0 0% 100%; /* White sidebar */
    --sidebar-foreground: 215 25% 35%; /* Dark text, slightly lighter than main foreground for softer look #4D5766 */
    --sidebar-primary: 199 78% 52%; /* Main primary blue for active/selected items */
    --sidebar-primary-foreground: 210 40% 98%; /* Light text on primary */
    --sidebar-accent: 206 27% 92%; /* Light gray hover, slightly darker than main bg #E8EFF3 */
    --sidebar-accent-foreground: 199 78% 48%; /* Primary blue, slightly darker for text on hover #239FD7 */
    --sidebar-border: 210 20% 88%; /* #DDE3E9 - Slightly lighter border for sidebar */
    --sidebar-ring: 199 78% 52%;
  }

  .dark {
    /* Define dark theme variables if needed, for now, focusing on light theme */
    --background: 215 28% 17%; /* Dark blue-gray background */
    --foreground: 210 40% 98%; /* Light gray text */

    --card: 215 28% 15%; /* Slightly darker card */
    --card-foreground: 210 40% 98%;

    --popover: 215 28% 15%;
    --popover-foreground: 210 40% 98%;

    --primary: 199 78% 52%; /* #29ABE2 */
    --primary-foreground: 210 40% 98%;

    --secondary: 215 28% 22%; /* Darker secondary */
    --secondary-foreground: 210 40% 98%;

    --muted: 215 28% 22%;
    --muted-foreground: 210 30% 70%;

    --accent: 26 87% 62%; /* #F2994A */
    --accent-foreground: 26 70% 15%;

    --border: 215 28% 25%;
    --input: 215 28% 20%;
    --ring: 199 78% 52%;

    --sidebar-background: 215 30% 12%; /* Very dark blue-gray for sidebar */
    --sidebar-foreground: 210 30% 85%; 
    --sidebar-primary: 199 78% 60%; 
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 30% 18%; 
    --sidebar-accent-foreground: 199 78% 70%; 
    --sidebar-border: 215 30% 22%;
    --sidebar-ring: 199 78% 52%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  /* RTL support for Arabic */
  [dir="rtl"] {
    direction: rtl;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  [dir="rtl"] .ml-auto {
    margin-left: 0;
    margin-right: auto;
  }

  [dir="rtl"] .mr-auto {
    margin-right: 0;
    margin-left: auto;
  }

  /* Sidebar RTL adjustments */
  [dir="rtl"] .sidebar {
    border-left: 1px solid hsl(var(--sidebar-border));
    border-right: none;
  }

  /* Icon adjustments for RTL */
  [dir="rtl"] .lucide {
    transform: scaleX(-1);
  }

  [dir="rtl"] .lucide-chevron-right {
    transform: scaleX(-1);
  }
}
